<template>
    <div>
        <div class="flex flex-row gap-2 items-center">
            <h3 class="text-base font-semibold text-gray-900">Completed Order Items</h3>
            <div
                class="bg-blue-500 text-white w-8 h-8 flex items-center justify-center rounded-full font-bold text-sm">
                {{ items.data?.length || 0 }}
            </div>
        </div>

        <div v-if="items.data?.length === 0" class="flex flex-col gap-3 mt-5 border border-gray-200 sm:p-5 bg-gray-100">
            <div class="text-center py-10">
                <NoDataMessage message1="No Completed Order Items Found." />
            </div>
        </div>

        <div v-else class="flex flex-col gap-3 mt-5 border border-gray-200 p-1 sm:p-5 bg-gray-100">
            <div v-for="item in items.data" :key="item.id"
                class="flex flex-col md:flex-row w-full border border-gray-200 bg-white">
                <div class="p-5 md:w-2/3">
                    <div class="flex flex-row justify-between">
                        <h3 class="text-base/7 font-semibold text-gray-900">{{ item.title }}</h3>
                    </div>
                    <div class="flex flex-col lg:flex-row ">
                        <div class="flex flex-col gap-y-3 mt-5 flex-1 ">
                            <div class="flex gap-x-3 border-b border-1 border-gray-100 items-center">
                                <dt class="text-sm/6 font-medium text-gray-900">Topic:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{{ item.topic }}</dd>
                            </div>
                            <div class="flex gap-x-3 border-b border-1 border-gray-100 items-center">
                                <dt class="text-sm/6 font-medium text-gray-900">Niche:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{{ item.niche }}</dd>
                            </div>
                            <div class="flex gap-x-3 border-b border-1 border-gray-100 items-center">
                                <dt class="text-sm/6 font-medium text-gray-900">Amount:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">${{ item.price_paid }}
                                </dd>
                            </div>
                        </div>
                        <div class="flex flex-col gap-y-3 mt-5 flex-1">
                            <div class="flex gap-x-3 border-b border-1 border-gray-100 items-center">
                                <dt class="text-sm/6 font-medium text-gray-900">Status:</dt>

                                <span class="px-2.5 py-0.5 text-xs font-medium rounded-full"
                                    :class="getStatusClass(item.stateName)">
                                    {{ item.stateLabel || 'Completed' }}
                                </span>

                            </div>
                            <div class="flex gap-x-3 border-b border-1 border-gray-100 items-center">
                                <dt class="text-sm/6 font-medium text-gray-900">Delivery Date:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{{ item.delivery_date }}
                                </dd>
                            </div>

                            <div class="flex gap-x-3 border-b border-1 border-gray-100 items-center">
                                <dt class="text-sm/6 font-medium text-gray-900">Customer Name:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{{ item.customer_name }}
                                </dd>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-5 lg:w-1/3 0">
                    <div class="px-4 sm:px-0 flex flex-col gap-4">
                        <h3 class="text-base/7 font-semibold text-gray-900">Order Item ID #{{ item.id }}</h3>

                        <div>
                            <Link :href="route('publisher.orders.details', { id: item.id })"
                                class="inline-flex items-center gap-x-1.5 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                            Order Details
                            <ChevronRight color="" :size="32" class="-mr-0.5 size-5" />
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            <PaginationLinks v-if="items.data.length" :links="items.links" />

        </div>
    </div>
</template>

<script setup>
import { ChevronRight } from 'lucide-vue-next';
import { Link } from '@inertiajs/vue3';
import { ref, onMounted, watch, inject } from 'vue';
import NoDataMessage from '@/Components/NoDataMessage.vue';
import { getStatusClass } from '@/helpers/utils.js';
import PaginationLinks from '@/Components/PaginationLinks.vue';

const page = inject("page");

const props = defineProps({
    items: {
        type: Object,
        required: true,
        default: () => ({
            data: [],
            meta: {
                current_page: 1,
                from: 1,
                last_page: 1,
                per_page: 10,
                to: 0,
                total: 0
            },
            links: []
        })
    }
});

const loading = ref(true);

// Add a watch to debug the items structure
watch(() => props.items, (newItems) => {
    console.log('Completed Items structure:', newItems);
}, { deep: true });

onMounted(() => {
    setTimeout(() => {
        loading.value = false;
    }, 1000);
});
</script>

<style lang="scss" scoped></style>