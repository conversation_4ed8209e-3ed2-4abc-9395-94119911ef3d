<template>
    <div>
        <div class="flex flex-row gap-2 items-center">
            <h3 class="text-base font-semibold text-gray-900">All Order Items</h3>
            <div
                class="bg-blue-500 text-white w-8 h-8 flex items-center justify-center rounded-full font-bold text-sm">
                {{ items.data?.length || 0 }}
            </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8">
            <div class="mt-5 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div v-if="items.data?.length === 0">
                        <div class="flex flex-col gap-y-3 text-sm border p-4">
                            <NoDataMessage message1="No completed order items found." />
                        </div>
                    </div>

                    <div v-else class="inline-block min-w-full align-middle border">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="">
                                <tr class="bg-gray-100">
                                    <th scope="col"
                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0 sm:px-6 lg:px-6">
                                        Order Item ID
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Website
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Customer
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Topic
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Status
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Amount
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Niche
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Date
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr v-for="item in items.data" :key="item.id">
                                    <td class="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0">
                                        <div class="flex items-center">
                                            <div class="ml-6">
                                                <div class="font-medium text-gray-900">#{{ item.id }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <div class="text-gray-900">{{ item.title }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <div class="text-gray-900">{{ item.customer_name }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <div class="mt-1 text-gray-500">{{ item.topic }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <span class="px-2.5 py-0.5 text-xs font-medium rounded-full"
                                            :class="getStatusClass(item.stateName)">
                                            {{ item.stateLabel || 'Completed' }}
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">${{
                                        item.price_paid
                                    }}</td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">{{ item.niche }}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">{{ formatDate(item.created_at) }}
                                    </td>

                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <div class="flex items-center">
                                            <Link :href="route('publisher.orders.details', { id: item.id })"
                                                class="inline-flex items-center gap-x-1.5 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                            Order Details
                                            <ChevronRight color="" :size="32" class="-mr-0.5 size-5" />
                                            </Link>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <PaginationLinks v-if="items.data.length" :links="items.links" class="mt-4" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ChevronRight } from 'lucide-vue-next';
import { Link } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import NoDataMessage from '@/Components/NoDataMessage.vue';
import { getStatusClass } from '@/helpers/utils.js';
import PaginationLinks from '@/Components/PaginationLinks.vue';

const props = defineProps({
    items: {
        type: Object,
        required: true,
        default: () => ({
            data: [],
            meta: {
                current_page: 1,
                from: 1,
                last_page: 1,
                per_page: 10,
                to: 0,
                total: 0
            },
            links: []
        })
    }
});

const loading = ref(true);

onMounted(() => {
    setTimeout(() => {
        loading.value = false;
    }, 1000);
});
</script>

<style lang="scss" scoped></style>