<?php

namespace Domain\Order\List;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use Illuminate\Support\Collection;

class GetOrderItemTimeline
{
    /**
     * @var array<int, array{state: string, label: string}>
     */
    protected array $steps = [
        ['state' => OrderItemStates::RequirementsPending->value,                  'label' => 'Order Placed'],
        ['state' => OrderItemStates::RequirementAwaitingPublisherApproval->value, 'label' => 'Requirements'],
        ['state' => OrderItemStates::ContentPending->value,                       'label' => 'Approve Requirements'],
        ['state' => OrderItemStates::ContentAwaitingPublisherApproval->value,     'label' => 'Content'],
        ['state' => OrderItemStates::PublicationInProcess->value,                 'label' => 'Approval'],
        ['state' => OrderItemStates::PublicationDelivered->value,                 'label' => 'Published'],
        ['state' => OrderItemStates::OrderItemCompleted->value,                   'label' => 'Completed'],
        ['state' => OrderItemStates::OrderItemCancelled->value,                   'label' => 'Cancelled'],
        ['state' => OrderItemStates::RefundedToWallet->value,                   'label' => 'Refunded'],
    ];

    /*******************************************************************
     * ORDER ITEM TIMELINE
     *********************************************************************
     *
     * Builds a sequential timeline of order-item steps.
     * Determines the current state index.
     * Marks all steps up to and including current as completed.
     * Returns an array of ['id','label','state','completed'] entries.
     *
     * @param  MarketplaceSingleOrderItem  $orderItem
     *   The order item whose state drives the timeline.
     *
     * @return array<int, array{id: int, label: string, state: string, completed: bool}>
     *   A list of timeline steps with completion flags.
     *
     *******************************************************************/
    public function handle(MarketplaceSingleOrderItem $orderItem): array
    {
        // Hide the opposite final state
        $excludeState = match ($orderItem->state_name) {
            OrderItemStates::OrderItemCompleted->value => OrderItemStates::OrderItemCancelled->value,
            OrderItemStates::OrderItemCancelled->value => OrderItemStates::RefundedToWallet->value,
            OrderItemStates::RefundedToWallet->value => OrderItemStates::OrderItemCompleted->value,
            default => null,
        };


        $filteredSteps = Collection::make($this->steps)
            ->filter(fn(array $step): bool => $step['state'] !== $excludeState);

        // Hide Some States
        if (in_array($orderItem->state_name, [
            OrderItemStates::OrderItemCancelled->value,
            OrderItemStates::RefundedToWallet->value,
        ])) {

            $hideStates = [
                OrderItemStates::ContentPending->value,
                OrderItemStates::ContentAwaitingPublisherApproval->value,
                OrderItemStates::PublicationInProcess->value,
                OrderItemStates::PublicationDelivered->value,
            ];

            $filteredSteps = $filteredSteps->filter(fn(array $step): bool => !in_array($step['state'], $hideStates));
        }

        // Find the zero-based index of the current state in filtered steps
        $currentIndex = $filteredSteps
            ->pluck('state')
            ->search($orderItem->state_name, true);

        // Build and return the timeline from filtered steps, auto-assigning IDs and completion flags
        return $filteredSteps
            ->values() // ensure continuous integer keys
            ->map(fn(array $step, int $index): array => [
                'id' => $index + 1,
                'label' => $step['label'],
                'state' => $step['state'],
                'completed' => $index <= $currentIndex,
            ])
            ->all();
    }
}
