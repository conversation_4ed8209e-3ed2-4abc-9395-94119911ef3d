<?php

namespace App\Http\Controllers\FilterQueries;

use Illuminate\Database\Eloquent\Builder;

trait QueriesTrait
{
    /*******************************************************************
     * APPLY SEARCH QUERY
     *********************************************************************
     *
     * Performs full-text search on websites in marketplace.
     * Combines full-text indexing for site_title & site_description
     * with domain matching for comprehensive search results.
     *
     * - Prioritizes full-text matches over domain matches
     * - Orders results by relevance score
     * - Uses natural language mode for better search accuracy
     *
     * @param Builder $queryScope
     * Query builder instance to apply search conditions
     *
     * @param string $searchTerm
     * Search term to match against website content
     *
     * @return Builder
     * Modified query builder with search conditions applied
     *
     ******************************************************************/
    public function applySearchQuery(Builder $queryScope, string $searchTerm)
    {
        // Remove http:// or https:// protocols from search term
        $searchTerm = preg_replace('/^https?:\/\//', '', $searchTerm);

        return $queryScope->where(function ($query) use ($searchTerm): void {
            // Full-text search on title and description with higher relevance
            $query->whereRaw(
                'MATCH(site_title, site_description) AGAINST(? IN NATURAL LANGUAGE MODE)',
                [$searchTerm],
            );
            // Add domain match as a fallback option
            $query->orWhere('website_domain', 'like', '%' . $searchTerm . '%');
        })
            ->orderByRaw(
                'MATCH(site_title, site_description) AGAINST(?) DESC, 
			IF(website_domain LIKE ?, 1, 0) DESC',
                [$searchTerm, '%' . $searchTerm . '%'],
            );
    }





    /*******************************************************************
     * APPLY NICHE QUERY
     *********************************************************************
     *
     * Filters websites based on niche pricing availability.
     * Websites with price > 0 are considered to accept publishing
     * in the specified niche.
     *
     * @param Builder $queryScope
     * Query builder instance to apply niche filter
     *
     * @param string $nichePrice
     * Column name representing the niche price
     *
     * @return Builder
     * Modified query builder with niche filter applied
     *
     ******************************************************************/
    public function applyNicheQuery(Builder $queryScope, string $nichePrice)
    {
        if ($nichePrice !== 'guest_post_price') {
            return $queryScope->where($nichePrice, '>', 0);
        }

        return $queryScope;
    }





    /*******************************************************************
     * GET NICHE COLUMN NAME
     *********************************************************************
     *
     * Maps requested niche to corresponding database column name.
     * Returns appropriate price column based on niche type.
     *
     * @param string $requestedNiche
     * The niche type to get column name for
     *
     * @return string
     * Database column name for the niche price
     *
     ******************************************************************/
    public function nicheColumnName(string $requestedNiche)
    {
        // -----------------------
        // Map Niche to Column
        return match ($requestedNiche) {
            'general' => 'guest_post_price',
            'casino' => 'casino_post_price',
            'cbd' => 'cbd_post_price',
            'crypto' => 'crypto_post_price',
            'adult' => 'adult_post_price',
            'finance' => 'finance_post_price',
            'dating' => 'dating_post_price',
            default => 'guest_post_price',
        };
    }





    /*******************************************************************
     * APPLY SORT QUERY
     *********************************************************************
     *
     * Applies sorting to query based on specified column and order.
     * Supports sorting by DR, traffic, price, spam score, language,
     * referring domains, linked domains, country, semrush, and domain age.
     *
     * @param Builder $queryScope
     * Query builder instance to apply sorting
     *
     * @param array $sort
     * Array containing sort column and order
     *
     * @param string $activeNiche
     * Current active niche for price sorting
     *
     * @return Builder
     * Modified query builder with sorting applied
     *
     ******************************************************************/
    public function applySortQuery(Builder $queryScope, array $sort, $activeNiche)
    {
        // -----------------------
        // Sort by Domain Rank
        if ($sort['column'] === 'dr') {
            return $queryScope->orderBy('ahref_domain_rank', $sort['order']);
        }

        // -----------------------
        // Sort by Traffic
        if ($sort['column'] === 'traffic') {
            return $queryScope->orderBy('ahref_organic_traffic', $sort['order']);
        }

        // -----------------------
        // Sort by Price
        if ($sort['column'] === 'price') {
            return $queryScope->orderBy($activeNiche, $sort['order']);
        }

        // -----------------------
        // Sort by Spam Score
        if ($sort['column'] === 'spam') {
            return $queryScope->orderBy('moz_spam_score', $sort['order']);
        }

        // -----------------------
        // Sort by Language
        if ($sort['column'] === 'language') {
            return $queryScope->orderBy('marketplace_website_languages.name', $sort['order']);
        }

        // -----------------------
        // Sort by Referring Domains
        if ($sort['column'] === 'referring') {
            return $queryScope->orderBy('reffering_domains_count', $sort['order']);
        }

        // -----------------------
        // Sort by Linked Domains (Outgoing Links)
        if ($sort['column'] === 'linked') {
            return $queryScope->orderBy('outgoing_links_count', $sort['order']);
        }

        // -----------------------
        // Sort by Top Traffic Country
        if ($sort['column'] === 'country') {
            return $queryScope->orderBy('countries_list.name', $sort['order']);
        }

        // -----------------------
        // Sort by SemRush Authority Score
        if ($sort['column'] === 'semrush') {
            return $queryScope->orderBy('semrush_authority_score', $sort['order']);
        }

        // -----------------------
        // Sort by Domain Age
        if ($sort['column'] === 'age') {
            return $queryScope->orderBy('domain_registration_date', $sort['order']);
        }

        return $queryScope;
    }





    /*******************************************************************
     * APPLY HOME QUERY
     *********************************************************************
     *
     * Applies default sorting for marketplace homepage.
     * Prioritizes websites based on specific criteria:
     * - Price range (100-300)
     * - Traffic range (100k-300k)
     * - Outgoing links count
     * - English language
     * - Tech-related content
     *
     * @param Builder $queryScope
     * Query builder instance to apply homepage sorting
     *
     * @return Builder
     * Modified query builder with homepage sorting applied
     *
     ******************************************************************/
    public function applyHomeQuery(Builder $queryScope)
    {
        // -----------------------
        // Apply Price Range Priority
        $queryScope = $queryScope->orderByRaw(
            'CASE WHEN guest_post_price BETWEEN 100 AND 300 THEN 0 ELSE 1 END',
        );

        // -----------------------
        // Apply Traffic Range Priority
        $queryScope = $queryScope->orderByRaw(
            'CASE WHEN ahref_organic_traffic BETWEEN 100000 AND 300000 THEN 0 ELSE 1 END',
        );

        // -----------------------
        // Apply Outgoing Links Priority
        $queryScope = $queryScope->orderByRaw(
            'CASE WHEN outgoing_links_count > 100 THEN 0 ELSE 1 END',
        );

        // -----------------------
        // Apply Language Priority
        $queryScope = $queryScope->orderByRaw(
            'CASE WHEN site_language_id = 1 THEN 0 ELSE 1 END',
        );

        // -----------------------
        // Apply Tech Content Priority
        return $queryScope->orderByRaw(
            "CASE WHEN site_title LIKE '%tech%' THEN 0 ELSE 1 END",
        );
    }
}
