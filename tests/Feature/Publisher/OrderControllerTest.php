<?php

namespace Tests\Feature\Publisher;

use App\Services\Publisher\PublisherOrderService;
use Tests\TestCase;

class OrderControllerTest extends TestCase
{

    protected User $publisher;
    protected User $advertiser;
    protected MarketplaceWebsite $website;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->publisher = User::factory()->create(['role' => Role::Publisher->value]);
        $this->advertiser = User::factory()->create(['role' => Role::Advertiser->value]);

        // Create a test website
        $this->website = MarketplaceWebsite::factory()->create([
            'publisher_user_id' => $this->publisher->id,
        ]);
    }

    /*********************************************************************
     * TEST ORDERS INDEX RETURNS CONSISTENT DATA STRUCTURE
     *********************************************************************
     *
     * Verifies that both pending and completed order items use the same
     * data structure for consistency in the UI.
     *
     *********************************************************************/
    public function test_orders_index_returns_consistent_data_structure(): void
    {
        // Create a pending order item
        $pendingOrder = MarketplaceOrder::factory()->create([
            'user_id' => $this->advertiser->id,
            'status' => MarketplaceOrderStatus::PENDING->value,
        ]);

        $pendingOrderItem = MarketplaceSingleOrderItem::factory()->create([
            'order_id' => $pendingOrder->id,
            'marketplace_website_id' => $this->website->id,
            'state' => OrderItemStates::ContentPending->value,
        ]);

        // Create a completed order item
        $completedOrder = MarketplaceOrder::factory()->create([
            'user_id' => $this->advertiser->id,
            'status' => MarketplaceOrderStatus::COMPLETED->value,
        ]);

        $completedOrderItem = MarketplaceSingleOrderItem::factory()->create([
            'order_id' => $completedOrder->id,
            'marketplace_website_id' => $this->website->id,
            'state' => OrderItemStates::OrderItemCompleted->value,
        ]);

        // Act as publisher and visit orders page
        $response = $this->actingAs($this->publisher)
            ->get(route('publisher.orders.index'));

        // Assert response is successful
        $response->assertStatus(200);

        // Get the props passed to the Vue component
        $props = $response->viewData('page')['props'];

        // Assert both pending and completed items have the same structure
        $this->assertArrayHasKey('pendingOrderItems', $props);
        $this->assertArrayHasKey('completedOrderItems', $props);

        // Both should be paginated collections with data, meta, and links
        $this->assertArrayHasKey('data', $props['pendingOrderItems']);
        $this->assertArrayHasKey('data', $props['completedOrderItems']);

        // If there are items, verify they have the same structure
        if (!empty($props['pendingOrderItems']['data'])) {
            $pendingItem = $props['pendingOrderItems']['data'][0];
            $this->assertArrayHasKey('id', $pendingItem);
            $this->assertArrayHasKey('title', $pendingItem);
            $this->assertArrayHasKey('topic', $pendingItem);
            $this->assertArrayHasKey('niche', $pendingItem);
            $this->assertArrayHasKey('price_paid', $pendingItem);
            $this->assertArrayHasKey('customer_name', $pendingItem);
        }

        if (!empty($props['completedOrderItems']['data'])) {
            $completedItem = $props['completedOrderItems']['data'][0];
            $this->assertArrayHasKey('id', $completedItem);
            $this->assertArrayHasKey('title', $completedItem);
            $this->assertArrayHasKey('topic', $completedItem);
            $this->assertArrayHasKey('niche', $completedItem);
            $this->assertArrayHasKey('price_paid', $completedItem);
            $this->assertArrayHasKey('customer_name', $completedItem);
        }
    }

    /*********************************************************************
     * TEST COMPLETED ORDER ITEMS ARE FILTERED CORRECTLY
     *********************************************************************
     *
     * Verifies that only completed order items are returned in the
     * completed section.
     *
     *********************************************************************/
    public function test_completed_order_items_are_filtered_correctly(): void
    {
        // Create orders with different states
        $order = MarketplaceOrder::factory()->create([
            'user_id' => $this->advertiser->id,
        ]);

        // Create a pending order item
        MarketplaceSingleOrderItem::factory()->create([
            'order_id' => $order->id,
            'marketplace_website_id' => $this->website->id,
            'state' => OrderItemStates::ContentPending->value,
        ]);

        // Create a completed order item
        MarketplaceSingleOrderItem::factory()->create([
            'order_id' => $order->id,
            'marketplace_website_id' => $this->website->id,
            'state' => OrderItemStates::OrderItemCompleted->value,
        ]);

        // Act as publisher and visit orders page
        $response = $this->actingAs($this->publisher)
            ->get(route('publisher.orders.index'));

        $props = $response->viewData('page')['props'];

        // Assert that pending items don't include completed ones
        $pendingItems = $props['pendingOrderItems']['data'];
        foreach ($pendingItems as $item) {
            $this->assertNotEquals(OrderItemStates::OrderItemCompleted->value, $item['stateName']);
        }

        // Assert that completed items only include completed ones
        $completedItems = $props['completedOrderItems']['data'];
        foreach ($completedItems as $item) {
            // The completed items should be delivered/completed
            $this->assertTrue(in_array($item['stateName'], [
                OrderItemStates::OrderItemCompleted->value,
                OrderItemStates::PublicationDelivered->value
            ]));
        }
    }
}
